["Test_suites/test_regression_suite.py::test_regression_checkboxes", "Test_suites/test_regression_suite.py::test_regression_dropdown", "Test_suites/test_regression_suite.py::test_regression_dynamic_controls_enable_disable", "Test_suites/test_regression_suite.py::test_regression_file_upload", "Test_suites/test_regression_suite.py::test_regression_login_logout", "test_regression_failures.py::test_regression_checkboxes", "test_regression_failures.py::test_regression_dropdown", "test_regression_failures.py::test_regression_dynamic_controls_enable_disable", "test_regression_failures.py::test_regression_file_upload", "test_regression_failures.py::test_regression_login_logout", "test_regression_suite.py::test_regression_checkboxes", "test_regression_suite.py::test_regression_dropdown", "test_regression_suite.py::test_regression_dynamic_controls_enable_disable", "test_regression_suite.py::test_regression_file_upload", "test_regression_suite.py::test_regression_login_logout", "test_suite.py::test_checkboxes_with_state_verification", "test_suite.py::test_context_menu_with_right_click", "test_suite.py::test_drag_and_drop_with_position_tracking", "test_suite.py::test_dropdown_with_all_selection_methods", "test_suite.py::test_dynamic_content_with_refreshes", "test_suite.py::test_dynamic_controls_with_ajax_waits", "test_suite.py::test_file_upload_with_verification", "test_suite.py::test_form_validation_comprehensive", "test_suite.py::test_horizontal_slider_with_precision", "test_suite.py::test_hovers_with_dynamic_content", "test_suite.py::test_javascript_alerts_with_interactions", "test_suite.py::test_key_presses_with_combinations", "test_suite.py::test_login_with_session_validation", "test_suite.py::test_nested_frames_content", "test_suite_extended.py::test_forgot_password", "test_suite_extended.py::test_shifting_content_menu", "test_suite_extended.py::test_status_codes_navigation", "test_suite_extended.py::test_wysiwyg_editor"]